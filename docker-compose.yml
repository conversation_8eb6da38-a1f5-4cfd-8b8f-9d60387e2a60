version: '3.8'

services:
  lazyremixer:
    build: .
    container_name: lazyremixer-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      # Persistent storage for uploads and output
      - lazyremixer-uploads:/app/uploads
      - lazyremixer-output:/app/output
      - lazyremixer-temp:/app/temp
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

volumes:
  lazyremixer-uploads:
    driver: local
  lazyremixer-output:
    driver: local
  lazyremixer-temp:
    driver: local
