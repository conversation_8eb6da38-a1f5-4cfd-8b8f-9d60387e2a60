/**
 * LazyRemixer - Main Application Class
 */
class LazyRemixer {
    constructor() {
        this.fileManager = new FileManager();
        this.videoProcessor = new VideoProcessor();
        this.uploadedFiles = [];
        this.currentSettings = {
            targetDuration: 60,
            quantity: 1
        };
        
        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        this.checkBrowserSupport();
        this.bindEvents();
        this.updateUI();
    }

    /**
     * Check browser support for required APIs
     */
    checkBrowserSupport() {
        if (!this.fileManager.isSupported()) {
            this.showError(
                'Your browser does not support the File System Access API. ' +
                'Please use Chrome 86+ or Edge 86+ for the best experience.'
            );
        }
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Folder selection
        document.getElementById('select-folder-btn').addEventListener('click', () => {
            this.selectVideoFolder();
        });

        // Download destination selection
        document.getElementById('select-destination-btn').addEventListener('click', () => {
            this.selectDownloadDestination();
        });

        // Settings
        const durationSlider = document.getElementById('target-duration');
        const quantitySlider = document.getElementById('video-quantity');

        durationSlider.addEventListener('input', (e) => {
            this.currentSettings.targetDuration = parseInt(e.target.value);
            document.getElementById('duration-value').textContent = `${e.target.value}s`;
            this.updateUI();
        });

        quantitySlider.addEventListener('input', (e) => {
            this.currentSettings.quantity = parseInt(e.target.value);
            document.getElementById('quantity-value').textContent = e.target.value;
            this.updateUI();
        });

        // Generation
        document.getElementById('generate-btn').addEventListener('click', () => {
            this.generateVideos();
        });

        // Cleanup
        document.getElementById('cleanup-btn').addEventListener('click', () => {
            this.cleanup();
        });

        // Modal events
        document.getElementById('close-error-modal').addEventListener('click', () => {
            this.hideError();
        });

        document.getElementById('error-ok-btn').addEventListener('click', () => {
            this.hideError();
        });

        // Close modal on outside click
        document.getElementById('error-modal').addEventListener('click', (e) => {
            if (e.target.id === 'error-modal') {
                this.hideError();
            }
        });
    }

    /**
     * Select video folder
     */
    async selectVideoFolder() {
        try {
            this.showLoading('Scanning for video files...');
            
            const result = await this.fileManager.selectVideoDirectory();
            
            // Update UI
            document.getElementById('folder-name').textContent = result.directoryName;
            document.getElementById('video-count').textContent = result.videoCount;
            document.getElementById('folder-info').classList.remove('hidden');
            
            // Upload files to server
            this.showLoading('Uploading video files...');
            this.uploadedFiles = await this.fileManager.uploadVideos((progress, message) => {
                document.getElementById('loading-message').textContent = message;
            });
            
            this.hideLoading();
            this.updateUI();
            
        } catch (error) {
            this.hideLoading();
            this.showError(error.message);
        }
    }

    /**
     * Select download destination
     */
    async selectDownloadDestination() {
        try {
            const result = await this.fileManager.selectDownloadDirectory();
            
            document.getElementById('destination-name').textContent = result.directoryName;
            document.getElementById('destination-info').classList.remove('hidden');
            
        } catch (error) {
            this.showError(error.message);
        }
    }

    /**
     * Generate videos
     */
    async generateVideos() {
        if (this.uploadedFiles.length === 0) {
            this.showError('Please select a video folder first.');
            return;
        }

        try {
            // Show progress container
            document.getElementById('progress-container').classList.remove('hidden');
            document.getElementById('results-container').classList.add('hidden');
            
            // Clear previous results
            document.getElementById('video-progress-list').innerHTML = '';
            document.getElementById('results-list').innerHTML = '';
            
            // Update generate button
            const generateBtn = document.getElementById('generate-btn');
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<span class="btn-icon">⏳</span>Generating...';

            await this.videoProcessor.generateVideos(
                this.uploadedFiles,
                this.currentSettings,
                {
                    onStart: () => {
                        document.getElementById('progress-title').textContent = 
                            `Generating ${this.currentSettings.quantity} video(s)...`;
                        document.getElementById('progress-status').textContent = 
                            'Initializing...';
                    },
                    onProgress: (data) => {
                        this.updateProgress(data);
                    },
                    onVideoReady: (video, videoNumber) => {
                        this.handleVideoReady(video, videoNumber);
                    },
                    onComplete: (videos) => {
                        this.handleGenerationComplete(videos);
                    },
                    onError: (error) => {
                        this.showError(error.message);
                    }
                }
            );

        } catch (error) {
            this.showError(error.message);
        } finally {
            // Reset generate button
            const generateBtn = document.getElementById('generate-btn');
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<span class="btn-icon">🎲</span>Generate Random Videos';
        }
    }

    /**
     * Update progress display
     */
    updateProgress(data) {
        const progressList = document.getElementById('video-progress-list');
        let progressItem = document.getElementById(`progress-${data.videoNumber}`);
        
        if (!progressItem) {
            progressItem = this.createProgressItem(data.videoNumber);
            progressList.appendChild(progressItem);
        }
        
        // Update progress
        const progressFill = progressItem.querySelector('.progress-fill');
        const progressPercentage = progressItem.querySelector('.progress-percentage');
        const progressMessage = progressItem.querySelector('.progress-message');
        
        progressFill.style.width = `${data.progress}%`;
        progressPercentage.textContent = `${Math.round(data.progress)}%`;
        progressMessage.textContent = data.message;
        
        // Update overall status
        document.getElementById('progress-status').textContent = 
            `Video ${data.videoNumber} - ${data.message}`;
    }

    /**
     * Create progress item element
     */
    createProgressItem(videoNumber) {
        const item = document.createElement('div');
        item.className = 'video-progress-item';
        item.id = `progress-${videoNumber}`;
        
        item.innerHTML = `
            <div class="progress-item-header">
                <span class="progress-item-title">Video ${videoNumber}</span>
                <span class="progress-percentage">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0%"></div>
            </div>
            <div class="progress-message">Initializing...</div>
        `;
        
        return item;
    }

    /**
     * Handle video ready event
     */
    async handleVideoReady(video, videoNumber) {
        // Mark progress as completed
        const progressItem = document.getElementById(`progress-${videoNumber}`);
        if (progressItem) {
            progressItem.classList.add('completed');
            progressItem.querySelector('.progress-fill').style.width = '100%';
            progressItem.querySelector('.progress-percentage').textContent = '100%';
            progressItem.querySelector('.progress-message').textContent = 'Complete!';
        }
        
        // Download video immediately
        try {
            const result = await this.fileManager.downloadVideo(video);
            this.showNotification(result.message);
        } catch (error) {
            console.error('Download error:', error);
            this.showError(`Failed to download video ${videoNumber}: ${error.message}`);
        }
        
        // Add to results
        this.addVideoResult(video, videoNumber);
    }

    /**
     * Add video to results display
     */
    addVideoResult(video, videoNumber) {
        const resultsList = document.getElementById('results-list');
        const resultsContainer = document.getElementById('results-container');
        
        resultsContainer.classList.remove('hidden');
        
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';
        
        resultItem.innerHTML = `
            <div class="result-header">
                <span class="result-title">Video ${videoNumber}</span>
                <span class="result-status success">Ready</span>
            </div>
            <div class="result-info">
                <div class="result-info-item">
                    <span class="label">Filename:</span>
                    <span class="value">${video.filename}</span>
                </div>
                <div class="result-info-item">
                    <span class="label">Size:</span>
                    <span class="value">${this.videoProcessor.formatFileSize(video.size)}</span>
                </div>
                <div class="result-info-item">
                    <span class="label">Clips:</span>
                    <span class="value">${video.clipCount}</span>
                </div>
            </div>
            <div class="result-actions">
                <button class="btn btn-primary btn-small" onclick="app.downloadVideoAgain('${video.filename}')">
                    <span class="btn-icon">💾</span>Download Again
                </button>
            </div>
        `;
        
        resultsList.appendChild(resultItem);
    }

    /**
     * Handle generation complete
     */
    handleGenerationComplete(videos) {
        document.getElementById('progress-status').textContent =
            `Complete! Generated ${videos.length} video(s)`;

        const stats = this.videoProcessor.getStatistics();
        if (stats) {
            this.showNotification(
                `Generation complete! Created ${stats.videoCount} videos with ${stats.totalClips} total clips.`
            );
        }
    }

    /**
     * Download video again
     */
    async downloadVideoAgain(filename) {
        try {
            const video = { filename };
            const result = await this.fileManager.downloadVideo(video);
            this.showNotification(result.message);
        } catch (error) {
            this.showError(`Failed to download video: ${error.message}`);
        }
    }

    /**
     * Cleanup files
     */
    async cleanup() {
        if (confirm('Are you sure you want to delete all uploaded and generated files?')) {
            try {
                this.showLoading('Cleaning up files...');

                const result = await this.videoProcessor.cleanup();

                // Clear UI
                this.uploadedFiles = [];
                document.getElementById('folder-info').classList.add('hidden');
                document.getElementById('progress-container').classList.add('hidden');
                document.getElementById('results-container').classList.add('hidden');

                this.hideLoading();
                this.updateUI();

                this.showNotification(result.message);

            } catch (error) {
                this.hideLoading();
                this.showError(error.message);
            }
        }
    }

    /**
     * Update UI state
     */
    updateUI() {
        const generateBtn = document.getElementById('generate-btn');
        const hasFiles = this.uploadedFiles.length > 0;

        generateBtn.disabled = !hasFiles || this.videoProcessor.getStatus().isProcessing;

        // Update settings display
        document.getElementById('duration-value').textContent = `${this.currentSettings.targetDuration}s`;
        document.getElementById('quantity-value').textContent = this.currentSettings.quantity;
    }

    /**
     * Show loading overlay
     */
    showLoading(message = 'Loading...') {
        document.getElementById('loading-message').textContent = message;
        document.getElementById('loading-overlay').classList.remove('hidden');
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    /**
     * Show error modal
     */
    showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').classList.remove('hidden');
    }

    /**
     * Hide error modal
     */
    hideError() {
        document.getElementById('error-modal').classList.add('hidden');
    }

    /**
     * Show notification (simple implementation)
     */
    showNotification(message) {
        // Create a simple notification
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--accent-secondary);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            z-index: 1002;
            max-width: 300px;
            word-wrap: break-word;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Add click to dismiss
        notification.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new LazyRemixer();
});

// Global error handler
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    if (window.app) {
        window.app.showError(`Unexpected error: ${event.error.message}`);
    }
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    if (window.app) {
        window.app.showError(`Unexpected error: ${event.reason.message || event.reason}`);
    }
    event.preventDefault();
});
