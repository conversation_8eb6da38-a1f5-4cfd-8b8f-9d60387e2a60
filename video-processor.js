/**
 * VideoProcessor - Handles video generation and server communication
 */
class VideoProcessor {
    constructor() {
        this.isProcessing = false;
        this.currentEventSource = null;
        this.generatedVideos = [];
    }

    /**
     * Generate videos with real-time progress updates
     */
    async generateVideos(files, settings, callbacks = {}) {
        if (this.isProcessing) {
            throw new Error('Video generation is already in progress');
        }

        const {
            onStart = () => {},
            onProgress = () => {},
            onVideoReady = () => {},
            onComplete = () => {},
            onError = () => {}
        } = callbacks;

        this.isProcessing = true;
        this.generatedVideos = [];

        try {
            // Validate inputs
            this.validateInputs(files, settings);

            // Prepare request data
            const requestData = {
                files: files,
                targetDuration: settings.targetDuration,
                quantity: settings.quantity
            };

            onStart();

            // Start Server-Sent Events connection
            const response = await fetch('/api/generate-videos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`Server error: ${response.status} ${response.statusText}`);
            }

            // Process Server-Sent Events
            await this.processServerSentEvents(response, {
                onProgress,
                onVideoReady,
                onError
            });

            onComplete(this.generatedVideos);

        } catch (error) {
            console.error('Video generation error:', error);
            onError(error);
        } finally {
            this.isProcessing = false;
            if (this.currentEventSource) {
                this.currentEventSource.close();
                this.currentEventSource = null;
            }
        }
    }

    /**
     * Process Server-Sent Events stream
     */
    async processServerSentEvents(response, callbacks) {
        const { onProgress, onVideoReady, onError } = callbacks;
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        try {
            while (true) {
                const { value, done } = await reader.read();
                
                if (done) {
                    break;
                }

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                
                // Keep the last incomplete line in the buffer
                buffer = lines.pop() || '';

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            await this.handleServerEvent(data, { onProgress, onVideoReady, onError });
                        } catch (parseError) {
                            console.error('Error parsing SSE data:', parseError, line);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
    }

    /**
     * Handle individual server events
     */
    async handleServerEvent(data, callbacks) {
        const { onProgress, onVideoReady, onError } = callbacks;

        switch (data.type) {
            case 'video-start':
                onProgress({
                    type: 'video-start',
                    videoNumber: data.videoNumber,
                    totalVideos: data.totalVideos,
                    progress: 0,
                    message: `Starting video ${data.videoNumber}...`
                });
                break;

            case 'progress':
                onProgress({
                    type: 'progress',
                    videoNumber: data.videoNumber,
                    progress: data.progress,
                    message: data.message
                });
                break;

            case 'video-ready':
                this.generatedVideos.push(data.video);
                onVideoReady(data.video, data.videoNumber);
                break;

            case 'error':
                onError(new Error(`Video ${data.videoNumber}: ${data.error}`));
                break;

            case 'complete':
                // Generation complete
                break;

            default:
                console.warn('Unknown event type:', data.type);
        }
    }

    /**
     * Validate input parameters
     */
    validateInputs(files, settings) {
        if (!files || files.length === 0) {
            throw new Error('No video files provided');
        }

        if (!settings.targetDuration || settings.targetDuration < 10 || settings.targetDuration > 600) {
            throw new Error('Target duration must be between 10 and 600 seconds');
        }

        if (!settings.quantity || settings.quantity < 1 || settings.quantity > 10) {
            throw new Error('Quantity must be between 1 and 10 videos');
        }

        // Validate file objects
        for (const file of files) {
            if (!file.id || !file.filename || !file.path) {
                throw new Error('Invalid file object structure');
            }
        }
    }

    /**
     * Cancel ongoing video generation
     */
    cancelGeneration() {
        if (this.currentEventSource) {
            this.currentEventSource.close();
            this.currentEventSource = null;
        }
        this.isProcessing = false;
    }

    /**
     * Clean up server files
     */
    async cleanup() {
        try {
            const response = await fetch('/api/cleanup', {
                method: 'DELETE'
            });

            if (!response.ok) {
                throw new Error(`Cleanup failed: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            this.generatedVideos = [];
            
            return result;
        } catch (error) {
            console.error('Cleanup error:', error);
            throw new Error(`Failed to cleanup files: ${error.message}`);
        }
    }

    /**
     * Get processing status
     */
    getStatus() {
        return {
            isProcessing: this.isProcessing,
            generatedCount: this.generatedVideos.length,
            generatedVideos: [...this.generatedVideos]
        };
    }

    /**
     * Format duration for display
     */
    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        
        if (minutes > 0) {
            return `${minutes}m ${remainingSeconds}s`;
        }
        return `${remainingSeconds}s`;
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Estimate processing time based on settings
     */
    estimateProcessingTime(settings) {
        // Rough estimation: 2-5 seconds per second of target duration per video
        const baseTimePerSecond = 3; // seconds
        const totalDuration = settings.targetDuration * settings.quantity;
        const estimatedSeconds = totalDuration * baseTimePerSecond;
        
        return {
            seconds: estimatedSeconds,
            formatted: this.formatDuration(estimatedSeconds)
        };
    }

    /**
     * Get video generation statistics
     */
    getStatistics() {
        if (this.generatedVideos.length === 0) {
            return null;
        }

        const totalSize = this.generatedVideos.reduce((sum, video) => sum + (video.size || 0), 0);
        const totalClips = this.generatedVideos.reduce((sum, video) => sum + (video.clipCount || 0), 0);

        return {
            videoCount: this.generatedVideos.length,
            totalSize: this.formatFileSize(totalSize),
            totalClips: totalClips,
            averageClipsPerVideo: Math.round(totalClips / this.generatedVideos.length),
            averageSize: this.formatFileSize(totalSize / this.generatedVideos.length)
        };
    }

    /**
     * Clear generated videos list
     */
    clearResults() {
        this.generatedVideos = [];
    }
}
