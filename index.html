<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LazyRemixer - Random Video Combiner</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <span class="icon">🎬</span>
                LazyRemixer
            </h1>
            <p class="subtitle">Randomly combine your videos into creative remixes</p>
        </header>

        <main class="main">
            <!-- File Selection Section -->
            <section class="section" id="file-section">
                <h2 class="section-title">
                    <span class="step-number">1</span>
                    Select Video Folder
                </h2>
                <div class="card">
                    <div class="folder-selector">
                        <button id="select-folder-btn" class="btn btn-primary">
                            <span class="btn-icon">📁</span>
                            Choose Video Folder
                        </button>
                        <div id="folder-info" class="folder-info hidden">
                            <div class="info-item">
                                <span class="label">Folder:</span>
                                <span id="folder-name" class="value"></span>
                            </div>
                            <div class="info-item">
                                <span class="label">Videos Found:</span>
                                <span id="video-count" class="value"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Download Destination Section -->
            <section class="section" id="destination-section">
                <h2 class="section-title">
                    <span class="step-number">2</span>
                    Download Destination
                </h2>
                <div class="card">
                    <div class="folder-selector">
                        <button id="select-destination-btn" class="btn btn-secondary">
                            <span class="btn-icon">💾</span>
                            Choose Download Folder
                        </button>
                        <div id="destination-info" class="folder-info hidden">
                            <div class="info-item">
                                <span class="label">Destination:</span>
                                <span id="destination-name" class="value"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Settings Section -->
            <section class="section" id="settings-section">
                <h2 class="section-title">
                    <span class="step-number">3</span>
                    Settings
                </h2>
                <div class="card">
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="target-duration" class="setting-label">
                                Target Duration (seconds)
                            </label>
                            <input 
                                type="range" 
                                id="target-duration" 
                                class="slider"
                                min="10" 
                                max="600" 
                                value="60"
                            >
                            <span id="duration-value" class="setting-value">60s</span>
                        </div>
                        
                        <div class="setting-item">
                            <label for="video-quantity" class="setting-label">
                                Number of Videos
                            </label>
                            <input 
                                type="range" 
                                id="video-quantity" 
                                class="slider"
                                min="1" 
                                max="10" 
                                value="1"
                            >
                            <span id="quantity-value" class="setting-value">1</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Generation Section -->
            <section class="section" id="generation-section">
                <h2 class="section-title">
                    <span class="step-number">4</span>
                    Generate Videos
                </h2>
                <div class="card">
                    <div class="generation-controls">
                        <button id="generate-btn" class="btn btn-success" disabled>
                            <span class="btn-icon">🎲</span>
                            Generate Random Videos
                        </button>
                        <button id="cleanup-btn" class="btn btn-danger">
                            <span class="btn-icon">🗑️</span>
                            Cleanup Files
                        </button>
                    </div>
                    
                    <div id="progress-container" class="progress-container hidden">
                        <div class="progress-header">
                            <h3 id="progress-title">Generating Videos...</h3>
                            <span id="progress-status" class="progress-status"></span>
                        </div>
                        
                        <div id="video-progress-list" class="video-progress-list">
                            <!-- Progress items will be added dynamically -->
                        </div>
                    </div>
                    
                    <div id="results-container" class="results-container hidden">
                        <h3 class="results-title">Generated Videos</h3>
                        <div id="results-list" class="results-list">
                            <!-- Results will be added dynamically -->
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer class="footer">
            <p>&copy; 2025 LazyRemixer - Creative Video Remixing Tool</p>
            <div class="footer-links">
                <span class="footer-note">
                    Requires Chrome 86+ or Edge 86+ for File System Access API
                </span>
            </div>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="spinner"></div>
            <p id="loading-message">Processing...</p>
        </div>
    </div>

    <!-- Error Modal -->
    <div id="error-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Error</h3>
                <button id="close-error-modal" class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p id="error-message"></p>
            </div>
            <div class="modal-footer">
                <button id="error-ok-btn" class="btn btn-primary">OK</button>
            </div>
        </div>
    </div>

    <script src="file-manager.js"></script>
    <script src="video-processor.js"></script>
    <script src="script.js"></script>
</body>
</html>
