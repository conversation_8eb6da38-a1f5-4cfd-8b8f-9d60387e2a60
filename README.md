# LazyRemixer 🎬

A modern web application for randomly combining video files into creative remixes. LazyRemixer uses server-side FFmpeg processing for reliable video manipulation and the File System Access API for seamless local file integration.

## Features

### 🎯 Core Functionality
- **Random Video Combination**: Intelligently combines clips from your video collection
- **Server-Side Processing**: Uses FFmpeg for reliable, high-performance video processing
- **Real-Time Progress**: Live updates during video generation with Server-Sent Events
- **Individual Downloads**: Videos download as they complete, no waiting for batches
- **File System Integration**: Direct folder selection using the File System Access API

### 🎨 User Experience
- **Dark Theme Design**: Modern, professional interface optimized for extended use
- **Responsive Layout**: Works seamlessly on desktop and mobile devices
- **Intuitive Workflow**: Simple 4-step process from folder selection to video generation
- **Error Handling**: Comprehensive error management with user-friendly messages

### ⚙️ Technical Features
- **Docker Ready**: Complete containerization for easy deployment
- **Multiple Formats**: Supports MP4, AVI, MOV, MKV, WebM video files
- **Configurable Settings**: Adjustable duration (10-600s) and quantity (1-10 videos)
- **Automatic Cleanup**: Built-in file management and cleanup utilities

## Quick Start

### Using Docker (Recommended)

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd LazyRemixer
   ```

2. **Start with Docker Compose**:
   ```bash
   docker-compose up --build
   ```

3. **Access the application**:
   Open http://localhost:3000 in your browser

### Manual Installation

1. **Prerequisites**:
   - Node.js 18+ 
   - npm or yarn

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start the server**:
   ```bash
   npm start
   ```

4. **Development mode**:
   ```bash
   npm run dev
   ```

## Usage Guide

### Step 1: Select Video Folder
- Click "Choose Video Folder" to select a directory containing your video files
- The app will scan for supported formats and display the count
- Videos are automatically uploaded to the server for processing

### Step 2: Choose Download Destination
- Select where you want generated videos to be saved
- This step is optional - videos can still be downloaded to your default download folder

### Step 3: Configure Settings
- **Target Duration**: Set the length of generated videos (10-600 seconds)
- **Number of Videos**: Choose how many random combinations to create (1-10)

### Step 4: Generate Videos
- Click "Generate Random Videos" to start the process
- Watch real-time progress for each video
- Videos download automatically as they complete
- View results and download again if needed

## Browser Compatibility

### Fully Supported
- **Chrome 86+**: Complete File System Access API support
- **Edge 86+**: Complete File System Access API support

### Partial Support
- **Firefox**: Basic functionality (no direct folder access)
- **Safari**: Basic functionality (no direct folder access)

*Note: For the best experience, use Chrome or Edge for full File System Access API support.*

## Technical Architecture

### Backend Stack
- **Node.js + Express**: Web server and API endpoints
- **FFmpeg**: Video processing engine
- **Multer**: File upload handling
- **Server-Sent Events**: Real-time progress updates

### Frontend Stack
- **Vanilla JavaScript**: No framework dependencies
- **File System Access API**: Direct file system integration
- **CSS3**: Modern dark theme design
- **Fetch API**: HTTP communication

### Video Processing Pipeline
1. **Clip Selection**: Random selection algorithm chooses diverse clips
2. **Extraction**: FFmpeg extracts clips with optimized settings
3. **Validation**: Ensures clips are valid and non-empty
4. **Concatenation**: Uses FFmpeg concat demuxer for reliable joining
5. **Cleanup**: Automatic removal of temporary files

## Configuration

### Environment Variables
```bash
PORT=3000                    # Server port (default: 3000)
NODE_ENV=production         # Environment mode
```

### Docker Configuration
The application includes production-ready Docker configuration with:
- Non-root user for security
- Health checks for monitoring
- Resource limits for stability
- Persistent volumes for data

## API Endpoints

### Video Operations
- `POST /api/upload-videos` - Upload video files
- `POST /api/generate-videos` - Generate random videos (SSE)
- `GET /api/download/:filename` - Download generated video
- `DELETE /api/cleanup` - Clean up temporary files

### System
- `GET /health` - Health check endpoint

## Development

### Project Structure
```
LazyRemixer/
├── server.js              # Main server application
├── routes/
│   └── video-routes.js    # Video processing API routes
├── index.html             # Main HTML page
├── style.css              # Dark theme styles
├── script.js              # Main application logic
├── file-manager.js        # File system integration
├── video-processor.js     # Video processing coordination
├── uploads/               # Temporary upload directory
├── output/                # Generated video output
├── temp/                  # Temporary processing files
├── Dockerfile             # Container configuration
├── docker-compose.yml     # Docker Compose setup
└── package.json           # Dependencies and scripts
```

### Key Components

#### FileManager Class
Handles File System Access API integration:
- Directory selection and scanning
- File upload coordination
- Download management with fallbacks

#### VideoProcessor Class
Manages video generation workflow:
- Server communication via SSE
- Progress tracking and callbacks
- Error handling and recovery

#### LazyRemixer Class
Main application controller:
- UI state management
- Event coordination
- User interaction handling

## Performance Considerations

### Server-Side Processing
- FFmpeg runs on the server for better performance and stability
- Optimized encoding settings for speed vs. quality balance
- Automatic resource cleanup prevents memory leaks

### File Handling
- Streaming uploads for large files
- Temporary file management with automatic cleanup
- Efficient progress reporting without blocking

### Browser Optimization
- Minimal JavaScript dependencies
- CSS animations with hardware acceleration
- Responsive design for various screen sizes

## Security Features

- **Input Validation**: All file types and parameters are validated
- **Path Sanitization**: Prevents directory traversal attacks
- **Non-Root Execution**: Docker container runs as non-privileged user
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **File Type Restrictions**: Only supported video formats are accepted

## Troubleshooting

### Common Issues

**"File System Access API not supported"**
- Use Chrome 86+ or Edge 86+ for full functionality
- Other browsers will fall back to standard file uploads

**"No suitable clips found"**
- Ensure video files are longer than 5 seconds
- Check that files are valid video formats
- Try with different source videos

**"Video generation timeout"**
- Large files may take longer to process
- Check server resources and FFmpeg installation
- Reduce target duration or number of videos

**Docker container won't start**
- Ensure Docker and Docker Compose are installed
- Check port 3000 is not already in use
- Verify sufficient disk space for video processing

### Logs and Debugging
- Server logs are available via `docker-compose logs`
- Browser console shows client-side errors
- Enable development mode for detailed logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For issues, questions, or feature requests, please open an issue on the project repository.

---

**LazyRemixer** - Transform your video collection into creative remixes with the power of randomness! 🎬✨
