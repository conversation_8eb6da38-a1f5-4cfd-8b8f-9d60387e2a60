# Use Node.js 18 Alpine for smaller image size
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm ci --omit=dev && npm cache clean --force

# Copy application files
COPY . .

# Create necessary directories
RUN mkdir -p uploads output temp

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S lazyremixer -u 1001 -G nodejs

# Change ownership of app directory
RUN chown -R lazyremixer:nodejs /app

# Switch to non-root user
USER lazyremixer

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Start the application
CMD ["npm", "start"]
