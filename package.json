{"name": "<PERSON><PERSON><PERSON>er", "version": "1.0.0", "description": "A web application for randomly combining video files into creative remixes", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "docker:build": "docker build -t lazy<PERSON><PERSON>er .", "docker:run": "docker run -p 3000:3000 lazyremixer", "docker:compose": "docker-compose up --build"}, "keywords": ["video", "remix", "ffmpeg", "random", "creative"], "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "fluent-ffmpeg": "^2.1.2", "ffmpeg-static": "^5.2.0", "ffprobe-static": "^3.1.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}