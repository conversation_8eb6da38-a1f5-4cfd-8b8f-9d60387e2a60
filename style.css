/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Dark Theme Colors */
    --bg-primary: #0f0f0f;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;
    --bg-card: #1e1e1e;
    --bg-hover: #333333;
    
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #666666;
    
    --accent-primary: #007acc;
    --accent-secondary: #28a745;
    --accent-danger: #dc3545;
    --accent-warning: #ffc107;
    
    --border-color: #333333;
    --border-hover: #555555;
    
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.4);
    
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    --transition: all 0.3s ease;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, <PERSON>xy<PERSON>, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
}

.title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.icon {
    font-size: 3rem;
    filter: drop-shadow(0 0 10px rgba(0, 122, 204, 0.3));
}

.subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    font-weight: 300;
}

/* Main Content */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Section Styles */
.section {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.6s ease forwards;
}

.section:nth-child(1) { animation-delay: 0.1s; }
.section:nth-child(2) { animation-delay: 0.2s; }
.section:nth-child(3) { animation-delay: 0.3s; }
.section:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    color: var(--text-primary);
}

.step-number {
    background: var(--accent-primary);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 700;
}

/* Card Styles */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.card:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-lg);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
    border-color: #0056b3;
}

.btn-secondary {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-hover);
    border-color: var(--border-hover);
}

.btn-success {
    background: var(--accent-secondary);
    border-color: var(--accent-secondary);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #218838;
    border-color: #218838;
}

.btn-danger {
    background: var(--accent-danger);
    border-color: var(--accent-danger);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
    border-color: #c82333;
}

.btn-icon {
    font-size: 1.1rem;
}

/* Folder Selector */
.folder-selector {
    text-align: center;
}

.folder-info {
    margin-top: 20px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.label {
    font-weight: 600;
    color: var(--text-secondary);
}

.value {
    color: var(--text-primary);
    font-weight: 500;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.setting-label {
    font-weight: 600;
    color: var(--text-primary);
}

.slider {
    -webkit-appearance: none;
    appearance: none;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    outline: none;
    border: 1px solid var(--border-color);
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--accent-primary);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
}

.slider::-webkit-slider-thumb:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--accent-primary);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.setting-value {
    font-weight: 600;
    color: var(--accent-primary);
    text-align: center;
    padding: 5px 10px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    min-width: 60px;
}

/* Generation Controls */
.generation-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

/* Progress Styles */
.progress-container {
    margin-top: 30px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.progress-header h3 {
    color: var(--text-primary);
    font-size: 1.3rem;
}

.progress-status {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.video-progress-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.video-progress-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: var(--transition);
}

.video-progress-item.completed {
    border-color: var(--accent-secondary);
    background: rgba(40, 167, 69, 0.1);
}

.video-progress-item.error {
    border-color: var(--accent-danger);
    background: rgba(220, 53, 69, 0.1);
}

.progress-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.progress-item-title {
    font-weight: 600;
    color: var(--text-primary);
}

.progress-percentage {
    font-weight: 600;
    color: var(--accent-primary);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-message {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Results Styles */
.results-container {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid var(--border-color);
}

.results-title {
    color: var(--text-primary);
    font-size: 1.3rem;
    margin-bottom: 20px;
}

.results-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.result-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    transition: var(--transition);
}

.result-item:hover {
    border-color: var(--border-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.result-title {
    font-weight: 600;
    color: var(--text-primary);
}

.result-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.result-status.success {
    background: rgba(40, 167, 69, 0.2);
    color: var(--accent-secondary);
}

.result-status.error {
    background: rgba(220, 53, 69, 0.2);
    color: var(--accent-danger);
}

.result-info {
    margin-bottom: 15px;
}

.result-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.result-info-item .label {
    color: var(--text-secondary);
}

.result-info-item .value {
    color: var(--text-primary);
}

.result-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* Footer */
.footer {
    margin-top: 60px;
    padding: 30px 0;
    text-align: center;
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

.footer-links {
    margin-top: 10px;
}

.footer-note {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--bg-tertiary);
    border-top: 4px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
}

.modal-content {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 30px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--text-primary);
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.modal-body {
    padding: 30px;
    color: var(--text-primary);
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-success {
    color: var(--accent-secondary);
}

.text-danger {
    color: var(--accent-danger);
}

.text-warning {
    color: var(--accent-warning);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }

    .icon {
        font-size: 2rem;
    }

    .settings-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .generation-controls {
        flex-direction: column;
        align-items: center;
    }

    .results-list {
        grid-template-columns: 1fr;
    }

    .card {
        padding: 20px;
    }

    .modal-content {
        width: 95%;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px 20px;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 1.5rem;
    }

    .section-title {
        font-size: 1.2rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .info-item,
    .result-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .progress-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .result-actions {
        width: 100%;
        justify-content: center;
    }
}
