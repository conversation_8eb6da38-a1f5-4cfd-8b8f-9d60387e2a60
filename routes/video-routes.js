const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const ffmpeg = require('fluent-ffmpeg');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const uniqueName = `${uuidv4()}-${file.originalname}`;
        cb(null, uniqueName);
    }
});

const upload = multer({
    storage,
    limits: {
        fileSize: 500 * 1024 * 1024 // 500MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
        const ext = path.extname(file.originalname).toLowerCase();
        if (allowedTypes.includes(ext)) {
            cb(null, true);
        } else {
            cb(new Error(`Unsupported file type: ${ext}`));
        }
    }
});

// Upload videos endpoint
router.post('/upload-videos', upload.array('videos', 50), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({ error: 'No video files uploaded' });
        }

        const uploadedFiles = req.files.map(file => ({
            id: uuidv4(),
            originalName: file.originalname,
            filename: file.filename,
            path: file.path,
            size: file.size
        }));

        res.json({
            success: true,
            message: `${uploadedFiles.length} videos uploaded successfully`,
            files: uploadedFiles
        });
    } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({ error: 'Failed to upload videos' });
    }
});

// Get video duration using ffprobe
const getVideoDuration = (filePath) => {
    return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(filePath, (err, metadata) => {
            if (err) {
                reject(err);
            } else {
                resolve(metadata.format.duration);
            }
        });
    });
};

// Generate random clips from videos
const selectRandomClips = async (files, targetDuration) => {
    const clips = [];
    let totalDuration = 0;
    const minClipDuration = 3; // Reduced minimum for more clips
    const maxClipDuration = Math.min(20, targetDuration / 3); // Ensure at least 3 clips possible
    const maxAttempts = files.length * 5; // Increased attempts
    let attempts = 0;

    console.log(`Selecting clips for ${targetDuration}s target duration from ${files.length} files`);

    // Shuffle files to get better variety
    const shuffledFiles = [...files].sort(() => Math.random() - 0.5);

    while (totalDuration < targetDuration && clips.length < 20 && attempts < maxAttempts) {
        attempts++;
        const randomFile = shuffledFiles[attempts % shuffledFiles.length];

        try {
            const videoDuration = await getVideoDuration(randomFile.path);
            console.log(`File ${randomFile.originalName}: duration ${videoDuration}s`);

            if (videoDuration < minClipDuration) {
                console.log(`Skipping ${randomFile.originalName}: too short (${videoDuration}s)`);
                continue;
            }

            const remainingDuration = targetDuration - totalDuration;
            const maxPossibleClip = Math.min(maxClipDuration, remainingDuration, videoDuration - 0.5);

            if (maxPossibleClip < minClipDuration) {
                console.log(`Breaking: remaining duration too small (${remainingDuration}s)`);
                break;
            }

            const clipDuration = Math.random() * (maxPossibleClip - minClipDuration) + minClipDuration;
            const maxStartTime = videoDuration - clipDuration;
            const startTime = Math.random() * maxStartTime;

            const clip = {
                file: randomFile,
                startTime: Math.max(0, Math.floor(startTime * 10) / 10), // Round to 1 decimal
                duration: Math.max(minClipDuration, Math.floor(clipDuration * 10) / 10) // Round to 1 decimal
            };

            clips.push(clip);
            totalDuration += clip.duration;

            console.log(`Added clip ${clips.length}: ${randomFile.originalName} (${clip.startTime}s-${clip.startTime + clip.duration}s, duration: ${clip.duration}s)`);

        } catch (error) {
            console.error(`Error processing ${randomFile.originalName}:`, error);
        }
    }

    console.log(`Selected ${clips.length} clips with total duration ${totalDuration}s (target: ${targetDuration}s)`);
    return clips;
};

// Extract clip using FFmpeg
const extractClip = (clip, outputPath) => {
    return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            reject(new Error(`Clip extraction timeout for ${clip.file.originalName}`));
        }, 90000); // Increased timeout

        console.log(`Extracting clip from ${clip.file.originalName}: ${clip.startTime}s for ${clip.duration}s`);

        ffmpeg(clip.file.path)
            .seekInput(clip.startTime)
            .duration(clip.duration)
            .outputOptions([
                '-c:v libx264',
                '-c:a aac',
                '-preset fast', // Changed from ultrafast for better compatibility
                '-crf 23',
                '-pix_fmt yuv420p', // Ensure compatible pixel format
                '-r 30', // Set consistent frame rate
                '-ar 44100', // Set consistent audio sample rate
                '-ac 2' // Ensure stereo audio
            ])
            .on('start', (commandLine) => {
                console.log('FFmpeg command:', commandLine);
            })
            .on('progress', (progress) => {
                console.log(`Clip extraction progress: ${progress.percent}%`);
            })
            .on('end', async () => {
                clearTimeout(timeout);
                try {
                    const stats = await fs.stat(outputPath);
                    if (stats.size === 0) {
                        throw new Error('Generated clip is empty');
                    }
                    console.log(`Clip extracted successfully: ${outputPath} (${stats.size} bytes)`);
                    resolve();
                } catch (error) {
                    reject(error);
                }
            })
            .on('error', (error) => {
                clearTimeout(timeout);
                console.error(`FFmpeg error for ${clip.file.originalName}:`, error);
                reject(error);
            })
            .save(outputPath);
    });
};

// Concatenate clips using FFmpeg
const concatenateClips = (clipPaths, outputPath) => {
    return new Promise(async (resolve, reject) => {
        try {
            if (clipPaths.length === 0) {
                throw new Error('No clips to concatenate');
            }

            if (clipPaths.length === 1) {
                // If only one clip, just copy it
                await fs.copyFile(clipPaths[0], outputPath);
                console.log(`Single clip copied to: ${outputPath}`);
                resolve();
                return;
            }

            const concatListPath = path.join('temp', `concat-${uuidv4()}.txt`);

            // Verify all clip files exist before creating concat list
            for (const clipPath of clipPaths) {
                try {
                    await fs.access(clipPath);
                } catch (error) {
                    throw new Error(`Clip file not found: ${clipPath}`);
                }
            }

            // Create concat list with proper escaping for Windows paths
            const concatContent = clipPaths.map(clipPath => {
                const absolutePath = path.resolve(clipPath);
                // Escape single quotes and backslashes for concat demuxer
                const escapedPath = absolutePath.replace(/'/g, "'\\''").replace(/\\/g, '/');
                return `file '${escapedPath}'`;
            }).join('\n');

            console.log('Concat list content:', concatContent);
            await fs.writeFile(concatListPath, concatContent);

            const timeout = setTimeout(() => {
                reject(new Error('Video concatenation timeout'));
            }, 180000); // Increased timeout

            console.log(`Concatenating ${clipPaths.length} clips to: ${outputPath}`);

            ffmpeg()
                .input(concatListPath)
                .inputOptions(['-f', 'concat', '-safe', '0'])
                .outputOptions([
                    '-c:v libx264', // Re-encode video for compatibility
                    '-c:a aac', // Re-encode audio for compatibility
                    '-preset fast',
                    '-crf 23',
                    '-pix_fmt yuv420p',
                    '-r 30',
                    '-ar 44100',
                    '-ac 2',
                    '-avoid_negative_ts', 'make_zero'
                ])
                .on('start', (commandLine) => {
                    console.log('FFmpeg concat command:', commandLine);
                })
                .on('progress', (progress) => {
                    console.log(`Concatenation progress: ${progress.percent}%`);
                })
                .on('end', async () => {
                    clearTimeout(timeout);
                    try {
                        await fs.unlink(concatListPath);
                        const stats = await fs.stat(outputPath);
                        console.log(`Concatenation complete: ${outputPath} (${stats.size} bytes)`);
                        resolve();
                    } catch (error) {
                        console.error('Error cleaning up concat file:', error);
                        resolve(); // Don't fail the whole process
                    }
                })
                .on('error', async (error) => {
                    clearTimeout(timeout);
                    console.error('FFmpeg concatenation error:', error);
                    try {
                        await fs.unlink(concatListPath);
                    } catch (cleanupError) {
                        console.error('Error cleaning up concat file:', cleanupError);
                    }
                    reject(error);
                })
                .save(outputPath);
        } catch (error) {
            console.error('Concatenation setup error:', error);
            reject(error);
        }
    });
};

// Generate a single video
const generateSingleVideo = async (files, targetDuration, videoNumber, progressCallback) => {
    const videoId = uuidv4();
    const tempClips = [];

    console.log(`Starting generation of video ${videoNumber} with target duration ${targetDuration}s`);

    try {
        progressCallback(10, `Selecting clips for video ${videoNumber}...`);

        const clips = await selectRandomClips(files, targetDuration);
        if (clips.length === 0) {
            throw new Error('No suitable clips found. Please ensure your videos are longer than 3 seconds.');
        }

        console.log(`Selected ${clips.length} clips for video ${videoNumber}`);
        progressCallback(20, `Selected ${clips.length} clips, starting extraction...`);

        // Extract clips
        for (let i = 0; i < clips.length; i++) {
            const clip = clips[i];
            const tempClipPath = path.join('temp', `clip-${videoId}-${i}.mp4`);

            progressCallback(20 + (i / clips.length) * 50, `Extracting clip ${i + 1}/${clips.length} from ${clip.file.originalName}...`);

            await extractClip(clip, tempClipPath);
            tempClips.push(tempClipPath);

            console.log(`Extracted clip ${i + 1}/${clips.length}: ${tempClipPath}`);
        }

        progressCallback(70, `Combining ${clips.length} clips into final video...`);

        // Concatenate clips
        const outputFilename = `remix-${videoId}.mp4`;
        const outputPath = path.join('output', outputFilename);

        console.log(`Starting concatenation of ${tempClips.length} clips`);
        await concatenateClips(tempClips, outputPath);

        progressCallback(90, 'Finalizing video...');

        // Verify output file
        const stats = await fs.stat(outputPath);
        if (stats.size === 0) {
            throw new Error('Generated video is empty');
        }

        console.log(`Video ${videoNumber} generated successfully: ${outputPath} (${stats.size} bytes, ${clips.length} clips)`);
        progressCallback(100, 'Video ready!');

        return {
            id: videoId,
            filename: outputFilename,
            path: outputPath,
            size: stats.size,
            clipCount: clips.length,
            duration: clips.reduce((sum, clip) => sum + clip.duration, 0)
        };

    } catch (error) {
        console.error(`Error generating video ${videoNumber}:`, error);
        throw new Error(`Failed to generate video ${videoNumber}: ${error.message}`);
    } finally {
        // Cleanup temporary clips
        console.log(`Cleaning up ${tempClips.length} temporary clips for video ${videoNumber}`);
        for (const clipPath of tempClips) {
            try {
                await fs.unlink(clipPath);
                console.log(`Cleaned up: ${clipPath}`);
            } catch (error) {
                console.error(`Error cleaning up ${clipPath}:`, error);
            }
        }
    }
};

// Generate videos endpoint with Server-Sent Events
router.post('/generate-videos', async (req, res) => {
    try {
        const { files, targetDuration = 60, quantity = 1 } = req.body;

        if (!files || files.length === 0) {
            return res.status(400).json({ error: 'No video files provided' });
        }

        if (quantity < 1 || quantity > 10) {
            return res.status(400).json({ error: 'Quantity must be between 1 and 10' });
        }

        if (targetDuration < 10 || targetDuration > 600) {
            return res.status(400).json({ error: 'Target duration must be between 10 and 600 seconds' });
        }

        // Set up Server-Sent Events
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        });

        const sendProgress = (data) => {
            const message = `data: ${JSON.stringify(data)}\n\n`;
            console.log('Sending SSE:', data);
            res.write(message);
        };

        // Generate videos
        for (let i = 0; i < quantity; i++) {
            try {
                sendProgress({
                    type: 'video-start',
                    videoNumber: i + 1,
                    totalVideos: quantity
                });

                const video = await generateSingleVideo(
                    files,
                    targetDuration,
                    i + 1,
                    (progress, message) => {
                        sendProgress({
                            type: 'progress',
                            videoNumber: i + 1,
                            progress,
                            message
                        });
                    }
                );

                sendProgress({
                    type: 'video-ready',
                    videoNumber: i + 1,
                    video
                });

            } catch (error) {
                sendProgress({
                    type: 'error',
                    videoNumber: i + 1,
                    error: error.message
                });
            }
        }

        sendProgress({ type: 'complete' });
        res.end();

    } catch (error) {
        console.error('Generate videos error:', error);
        res.write(`data: ${JSON.stringify({
            type: 'error',
            error: error.message
        })}\n\n`);
        res.end();
    }
});

// Download video endpoint
router.get('/download/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        const filePath = path.join('output', filename);

        // Verify file exists
        await fs.access(filePath);

        res.download(filePath, filename, (error) => {
            if (error) {
                console.error('Download error:', error);
                res.status(500).json({ error: 'Failed to download file' });
            }
        });
    } catch (error) {
        console.error('File not found:', error);
        res.status(404).json({ error: 'File not found' });
    }
});

// Cleanup endpoint
router.delete('/cleanup', async (req, res) => {
    try {
        const directories = ['uploads', 'output', 'temp'];
        let cleanedFiles = 0;

        for (const dir of directories) {
            try {
                const files = await fs.readdir(dir);
                for (const file of files) {
                    await fs.unlink(path.join(dir, file));
                    cleanedFiles++;
                }
            } catch (error) {
                console.error(`Error cleaning ${dir}:`, error);
            }
        }

        res.json({
            success: true,
            message: `Cleaned up ${cleanedFiles} files`
        });
    } catch (error) {
        console.error('Cleanup error:', error);
        res.status(500).json({ error: 'Failed to cleanup files' });
    }
});

module.exports = router;
