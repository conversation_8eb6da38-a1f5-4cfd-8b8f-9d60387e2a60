const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const ffmpeg = require('fluent-ffmpeg');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const uniqueName = `${uuidv4()}-${file.originalname}`;
        cb(null, uniqueName);
    }
});

const upload = multer({
    storage,
    limits: {
        fileSize: 500 * 1024 * 1024 // 500MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['.mp4', '.avi', '.mov', '.mkv', '.webm'];
        const ext = path.extname(file.originalname).toLowerCase();
        if (allowedTypes.includes(ext)) {
            cb(null, true);
        } else {
            cb(new Error(`Unsupported file type: ${ext}`));
        }
    }
});

// Upload videos endpoint
router.post('/upload-videos', upload.array('videos', 50), async (req, res) => {
    try {
        if (!req.files || req.files.length === 0) {
            return res.status(400).json({ error: 'No video files uploaded' });
        }

        const uploadedFiles = req.files.map(file => ({
            id: uuidv4(),
            originalName: file.originalname,
            filename: file.filename,
            path: file.path,
            size: file.size
        }));

        res.json({
            success: true,
            message: `${uploadedFiles.length} videos uploaded successfully`,
            files: uploadedFiles
        });
    } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({ error: 'Failed to upload videos' });
    }
});

// Get video duration using ffprobe
const getVideoDuration = (filePath) => {
    return new Promise((resolve, reject) => {
        ffmpeg.ffprobe(filePath, (err, metadata) => {
            if (err) {
                reject(err);
            } else {
                resolve(metadata.format.duration);
            }
        });
    });
};

// Generate random clips from videos
const selectRandomClips = async (files, targetDuration) => {
    const clips = [];
    let totalDuration = 0;
    const minClipDuration = 5;
    const maxClipDuration = 30;

    while (totalDuration < targetDuration && clips.length < files.length * 3) {
        const randomFile = files[Math.floor(Math.random() * files.length)];
        
        try {
            const videoDuration = await getVideoDuration(randomFile.path);
            
            if (videoDuration < minClipDuration) continue;
            
            const remainingDuration = targetDuration - totalDuration;
            const maxPossibleClip = Math.min(maxClipDuration, remainingDuration, videoDuration - 1);
            
            if (maxPossibleClip < minClipDuration) break;
            
            const clipDuration = Math.random() * (maxPossibleClip - minClipDuration) + minClipDuration;
            const startTime = Math.random() * (videoDuration - clipDuration);
            
            clips.push({
                file: randomFile,
                startTime: Math.floor(startTime),
                duration: Math.floor(clipDuration)
            });
            
            totalDuration += clipDuration;
        } catch (error) {
            console.error(`Error processing ${randomFile.originalName}:`, error);
        }
    }
    
    return clips;
};

// Extract clip using FFmpeg
const extractClip = (clip, outputPath) => {
    return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
            reject(new Error(`Clip extraction timeout for ${clip.file.originalName}`));
        }, 60000);

        ffmpeg(clip.file.path)
            .seekInput(clip.startTime)
            .duration(clip.duration)
            .outputOptions([
                '-c:v libx264',
                '-c:a aac',
                '-preset ultrafast',
                '-crf 23'
            ])
            .on('end', async () => {
                clearTimeout(timeout);
                try {
                    const stats = await fs.stat(outputPath);
                    if (stats.size === 0) {
                        throw new Error('Generated clip is empty');
                    }
                    resolve();
                } catch (error) {
                    reject(error);
                }
            })
            .on('error', (error) => {
                clearTimeout(timeout);
                reject(error);
            })
            .save(outputPath);
    });
};

// Concatenate clips using FFmpeg
const concatenateClips = (clipPaths, outputPath) => {
    return new Promise(async (resolve, reject) => {
        try {
            const concatListPath = path.join('temp', `concat-${uuidv4()}.txt`);
            const concatContent = clipPaths.map(clipPath => `file '${path.resolve(clipPath)}'`).join('\n');
            
            await fs.writeFile(concatListPath, concatContent);
            
            const timeout = setTimeout(() => {
                reject(new Error('Video concatenation timeout'));
            }, 120000);

            ffmpeg()
                .input(concatListPath)
                .inputOptions(['-f', 'concat', '-safe', '0'])
                .outputOptions([
                    '-c', 'copy',
                    '-avoid_negative_ts', 'make_zero'
                ])
                .on('end', async () => {
                    clearTimeout(timeout);
                    try {
                        await fs.unlink(concatListPath);
                        resolve();
                    } catch (error) {
                        console.error('Error cleaning up concat file:', error);
                        resolve(); // Don't fail the whole process
                    }
                })
                .on('error', async (error) => {
                    clearTimeout(timeout);
                    try {
                        await fs.unlink(concatListPath);
                    } catch (cleanupError) {
                        console.error('Error cleaning up concat file:', cleanupError);
                    }
                    reject(error);
                })
                .save(outputPath);
        } catch (error) {
            reject(error);
        }
    });
};

// Generate a single video
const generateSingleVideo = async (files, targetDuration, videoNumber, progressCallback) => {
    const videoId = uuidv4();
    const tempClips = [];

    try {
        progressCallback(10, `Selecting clips for video ${videoNumber}...`);

        const clips = await selectRandomClips(files, targetDuration);
        if (clips.length === 0) {
            throw new Error('No suitable clips found');
        }

        progressCallback(30, `Extracting ${clips.length} clips...`);

        // Extract clips
        for (let i = 0; i < clips.length; i++) {
            const clip = clips[i];
            const tempClipPath = path.join('temp', `clip-${videoId}-${i}.mp4`);

            await extractClip(clip, tempClipPath);
            tempClips.push(tempClipPath);

            const progress = 30 + (i / clips.length) * 40;
            progressCallback(progress, `Extracted clip ${i + 1}/${clips.length}`);
        }

        progressCallback(70, 'Combining clips...');

        // Concatenate clips
        const outputFilename = `remix-${videoId}.mp4`;
        const outputPath = path.join('output', outputFilename);

        await concatenateClips(tempClips, outputPath);

        progressCallback(90, 'Finalizing video...');

        // Verify output file
        const stats = await fs.stat(outputPath);
        if (stats.size === 0) {
            throw new Error('Generated video is empty');
        }

        progressCallback(100, 'Video ready!');

        return {
            id: videoId,
            filename: outputFilename,
            path: outputPath,
            size: stats.size,
            clipCount: clips.length
        };

    } catch (error) {
        console.error(`Error generating video ${videoNumber}:`, error);
        throw error;
    } finally {
        // Cleanup temporary clips
        for (const clipPath of tempClips) {
            try {
                await fs.unlink(clipPath);
            } catch (error) {
                console.error(`Error cleaning up ${clipPath}:`, error);
            }
        }
    }
};

// Generate videos endpoint with Server-Sent Events
router.post('/generate-videos', async (req, res) => {
    try {
        const { files, targetDuration = 60, quantity = 1 } = req.body;

        if (!files || files.length === 0) {
            return res.status(400).json({ error: 'No video files provided' });
        }

        if (quantity < 1 || quantity > 10) {
            return res.status(400).json({ error: 'Quantity must be between 1 and 10' });
        }

        if (targetDuration < 10 || targetDuration > 600) {
            return res.status(400).json({ error: 'Target duration must be between 10 and 600 seconds' });
        }

        // Set up Server-Sent Events
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        });

        const sendProgress = (data) => {
            res.write(`data: ${JSON.stringify(data)}\n\n`);
        };

        // Generate videos
        for (let i = 0; i < quantity; i++) {
            try {
                sendProgress({
                    type: 'video-start',
                    videoNumber: i + 1,
                    totalVideos: quantity
                });

                const video = await generateSingleVideo(
                    files,
                    targetDuration,
                    i + 1,
                    (progress, message) => {
                        sendProgress({
                            type: 'progress',
                            videoNumber: i + 1,
                            progress,
                            message
                        });
                    }
                );

                sendProgress({
                    type: 'video-ready',
                    videoNumber: i + 1,
                    video
                });

            } catch (error) {
                sendProgress({
                    type: 'error',
                    videoNumber: i + 1,
                    error: error.message
                });
            }
        }

        sendProgress({ type: 'complete' });
        res.end();

    } catch (error) {
        console.error('Generate videos error:', error);
        res.write(`data: ${JSON.stringify({
            type: 'error',
            error: error.message
        })}\n\n`);
        res.end();
    }
});

// Download video endpoint
router.get('/download/:filename', async (req, res) => {
    try {
        const { filename } = req.params;
        const filePath = path.join('output', filename);

        // Verify file exists
        await fs.access(filePath);

        res.download(filePath, filename, (error) => {
            if (error) {
                console.error('Download error:', error);
                res.status(500).json({ error: 'Failed to download file' });
            }
        });
    } catch (error) {
        console.error('File not found:', error);
        res.status(404).json({ error: 'File not found' });
    }
});

// Cleanup endpoint
router.delete('/cleanup', async (req, res) => {
    try {
        const directories = ['uploads', 'output', 'temp'];
        let cleanedFiles = 0;

        for (const dir of directories) {
            try {
                const files = await fs.readdir(dir);
                for (const file of files) {
                    await fs.unlink(path.join(dir, file));
                    cleanedFiles++;
                }
            } catch (error) {
                console.error(`Error cleaning ${dir}:`, error);
            }
        }

        res.json({
            success: true,
            message: `Cleaned up ${cleanedFiles} files`
        });
    } catch (error) {
        console.error('Cleanup error:', error);
        res.status(500).json({ error: 'Failed to cleanup files' });
    }
});

module.exports = router;
